[2025-07-28 13:38:03] train_n100.py(133) : DEBUG_MODE: False
[2025-07-28 13:38:03] train_n100.py(134) : USE_CUDA: True, CUDA_DEVICE_NUM: 0
[2025-07-28 13:38:03] train_n100.py(135) : env_params{'problem_size': 100, 'pomo_size': 100}
[2025-07-28 13:38:03] train_n100.py(135) : model_params{'embedding_dim': 128, 'sqrt_embedding_dim': 11.313708498984761, 'encoder_layer_num': 6, 'qkv_dim': 16, 'head_num': 8, 'logit_clipping': 10, 'ff_hidden_dim': 512, 'eval_type': 'argmax'}
[2025-07-28 13:38:03] train_n100.py(135) : optimizer_params{'optimizer': {'lr': 0.0001, 'weight_decay': 1e-06}, 'scheduler': {'milestones': [3001], 'gamma': 0.1}}
[2025-07-28 13:38:03] train_n100.py(135) : trainer_params{'use_cuda': True, 'cuda_device_num': 0, 'epochs': 3100, 'train_episodes': 100000, 'train_batch_size': 64, 'logging': {'model_save_interval': 100, 'img_save_interval': 100, 'log_image_params_1': {'json_foldername': 'log_image_style', 'filename': 'style_tsp_100.json'}, 'log_image_params_2': {'json_foldername': 'log_image_style', 'filename': 'style_loss_1.json'}}, 'model_load': {'enable': False}}
[2025-07-28 13:38:03] train_n100.py(135) : logger_params{'log_file': {'desc': 'train__tsp_n100__3000epoch', 'filename': 'log.txt', 'filepath': './result/20250728_223803_train__tsp_n100__3000epoch'}}
[2025-07-28 13:38:13] TSPTrainer.py(68) : =================================================================
