[2025-07-28 15:04:56] train_n100.py(133) : DEBUG_MODE: False
[2025-07-28 15:04:56] train_n100.py(134) : USE_CUDA: True, CUDA_DEVICE_NUM: 0
[2025-07-28 15:04:56] train_n100.py(135) : env_params{'problem_size': 100, 'pomo_size': 100}
[2025-07-28 15:04:56] train_n100.py(135) : model_params{'embedding_dim': 128, 'sqrt_embedding_dim': 11.313708498984761, 'encoder_layer_num': 6, 'qkv_dim': 16, 'head_num': 8, 'logit_clipping': 10, 'ff_hidden_dim': 512, 'eval_type': 'argmax'}
[2025-07-28 15:04:56] train_n100.py(135) : optimizer_params{'optimizer': {'lr': 0.0001, 'weight_decay': 1e-06}, 'scheduler': {'milestones': [3001], 'gamma': 0.1}}
[2025-07-28 15:04:56] train_n100.py(135) : trainer_params{'use_cuda': True, 'cuda_device_num': 0, 'epochs': 3100, 'train_episodes': 100000, 'train_batch_size': 64, 'logging': {'model_save_interval': 100, 'img_save_interval': 100, 'log_image_params_1': {'json_foldername': 'log_image_style', 'filename': 'style_tsp_100.json'}, 'log_image_params_2': {'json_foldername': 'log_image_style', 'filename': 'style_loss_1.json'}}, 'model_load': {'enable': False}}
[2025-07-28 15:04:56] train_n100.py(135) : logger_params{'log_file': {'desc': 'train__tsp_n100__3000epoch', 'filename': 'log.txt', 'filepath': './result/20250729_000456_train__tsp_n100__3000epoch'}}
[2025-07-28 15:05:03] TSPTrainer.py(68) : =================================================================
[2025-07-28 15:05:07] TSPTrainer.py(143) : Epoch   1: Train  64/100000(0.1%)  Score: 35.5877,  Loss: -20.0128
[2025-07-28 15:05:07] TSPTrainer.py(143) : Epoch   1: Train 128/100000(0.1%)  Score: 31.3423,  Loss: -19.4845
[2025-07-28 15:05:07] TSPTrainer.py(143) : Epoch   1: Train 192/100000(0.2%)  Score: 28.3632,  Loss: -18.3830
[2025-07-28 15:05:07] TSPTrainer.py(143) : Epoch   1: Train 256/100000(0.3%)  Score: 26.2292,  Loss: -16.7751
[2025-07-28 15:05:08] TSPTrainer.py(143) : Epoch   1: Train 320/100000(0.3%)  Score: 24.7238,  Loss: -15.2320
[2025-07-28 15:05:08] TSPTrainer.py(143) : Epoch   1: Train 384/100000(0.4%)  Score: 23.5688,  Loss: -13.9490
[2025-07-28 15:05:08] TSPTrainer.py(143) : Epoch   1: Train 448/100000(0.4%)  Score: 22.6545,  Loss: -12.9018
[2025-07-28 15:05:08] TSPTrainer.py(143) : Epoch   1: Train 512/100000(0.5%)  Score: 21.9024,  Loss: -12.1095
[2025-07-28 15:05:08] TSPTrainer.py(143) : Epoch   1: Train 576/100000(0.6%)  Score: 21.2951,  Loss: -11.4731
[2025-07-28 15:05:09] TSPTrainer.py(143) : Epoch   1: Train 640/100000(0.6%)  Score: 20.8006,  Loss: -10.9381
